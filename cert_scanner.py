#!/usr/bin/env python3
"""
证书扫描工具 - 自动化获取网站证书并查询使用同证书的网站
使用curl_cffi绕过可能的人机验证
"""

import re
import json
import time
import argparse
import os
import sys
from urllib.parse import urlparse, quote
from curl_cffi import requests
from bs4 import BeautifulSoup

# 尝试导入配置文件
try:
    import config
    CENSYS_COOKIE = getattr(config, 'CENSYS_COOKIE', None)
    REQUEST_TIMEOUT = getattr(config, 'REQUEST_TIMEOUT', 30)
    REQUEST_DELAY = getattr(config, 'REQUEST_DELAY', 1)
    VERBOSE = getattr(config, 'VERBOSE', True)
except ImportError:
    CENSYS_COOKIE = None
    REQUEST_TIMEOUT = 30
    REQUEST_DELAY = 1
    VERBOSE = True


class CertScanner:
    def __init__(self, cookie=None, verbose=True):
        """
        初始化证书扫描器

        Args:
            cookie (str): Censys平台的cookie字符串
            verbose (bool): 是否显示详细信息
        """
        self.session = requests.Session(impersonate="chrome110")
        self.cookie = cookie or CENSYS_COOKIE
        self.verbose = verbose

        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })

        if self.cookie:
            self.session.headers['Cookie'] = self.cookie

    def log(self, message, level="INFO"):
        """输出日志信息"""
        if self.verbose:
            timestamp = time.strftime('%H:%M:%S')
            print(f"[{timestamp}] {message}")

    def extract_domain(self, url):
        """从URL中提取域名"""
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
        
        parsed = urlparse(url)
        return parsed.netloc

    def query_crt_sh(self, domain):
        """
        查询crt.sh获取证书信息

        Args:
            domain (str): 目标域名

        Returns:
            tuple: (first_id, last_id) 第一个和最后一个证书ID
        """
        self.log(f"正在查询 {domain} 的证书信息...")

        url = f"https://crt.sh/?q={quote(domain)}"

        try:
            response = self.session.get(url, timeout=REQUEST_TIMEOUT)
            response.raise_for_status()

            # 添加请求延迟
            time.sleep(REQUEST_DELAY)

            # 解析HTML获取证书ID
            soup = BeautifulSoup(response.text, 'html.parser')

            # 查找包含证书ID的表格
            cert_ids = []
            for link in soup.find_all('a', href=True):
                href = link.get('href')
                if href and '?id=' in href:
                    cert_id = href.split('?id=')[1]
                    if cert_id.isdigit():
                        cert_ids.append(cert_id)

            if not cert_ids:
                self.log(f"未找到 {domain} 的证书信息", "ERROR")
                return None, None

            first_id = cert_ids[0]
            last_id = cert_ids[-1]

            self.log(f"找到证书ID: 第一个={first_id}, 最后一个={last_id}")
            return first_id, last_id

        except Exception as e:
            self.log(f"查询crt.sh失败: {e}", "ERROR")
            return None, None

    def get_cert_sha256(self, cert_id):
        """
        根据证书ID获取SHA-256指纹
        
        Args:
            cert_id (str): 证书ID
            
        Returns:
            str: SHA-256指纹
        """
        print(f"[+] 正在获取证书ID {cert_id} 的SHA-256指纹...")
        
        url = f"https://crt.sh/?id={cert_id}"
        
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            
            # 查找SHA-256指纹
            sha256_pattern = r'SHA-256.*?([a-fA-F0-9]{64})'
            match = re.search(sha256_pattern, response.text, re.IGNORECASE | re.DOTALL)
            
            if match:
                sha256 = match.group(1).upper()
                print(f"[+] 找到SHA-256指纹: {sha256}")
                return sha256
            else:
                print(f"[-] 未找到证书ID {cert_id} 的SHA-256指纹")
                return None
                
        except Exception as e:
            print(f"[-] 获取SHA-256指纹失败: {e}")
            return None

    def search_censys(self, sha256):
        """
        在Censys平台搜索使用相同证书的域名
        
        Args:
            sha256 (str): 证书SHA-256指纹
            
        Returns:
            list: 使用相同证书的域名列表
        """
        print(f"[+] 正在Censys平台搜索使用证书 {sha256} 的域名...")
        
        if not self.cookie:
            print("[-] 警告: 未提供Censys cookie，可能无法访问搜索结果")
        
        search_query = f'cert.fingerprint_sha256="{sha256}"'
        url = f"https://platform.censys.io/search?q={quote(search_query)}"
        
        try:
            # 添加额外的反检测头
            headers = {
                'Referer': 'https://platform.censys.io/',
                'Sec-Fetch-Dest': 'document',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-User': '?1',
            }
            
            response = self.session.get(url, headers=headers, timeout=30)
            
            if response.status_code == 403:
                print("[-] 访问被拒绝，可能遇到Cloudflare保护或需要登录")
                return []
            
            response.raise_for_status()
            
            # 解析搜索结果
            domains = self.parse_censys_results(response.text)
            
            if domains:
                print(f"[+] 找到 {len(domains)} 个使用相同证书的域名")
            else:
                print("[-] 未找到使用相同证书的域名")
            
            return domains
            
        except Exception as e:
            print(f"[-] Censys搜索失败: {e}")
            return []

    def parse_censys_results(self, html_content):
        """
        解析Censys搜索结果页面
        
        Args:
            html_content (str): HTML内容
            
        Returns:
            list: 域名列表
        """
        domains = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 查找包含域名的元素（可能需要根据实际页面结构调整）
            # 这里使用多种可能的选择器
            selectors = [
                'td[data-label="Name"]',
                '.name-cell',
                '[data-testid="name-cell"]',
                'td:contains(".")',
            ]
            
            for selector in selectors:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text().strip()
                    # 简单的域名验证
                    if '.' in text and not text.startswith('http') and len(text) < 100:
                        domains.append(text)
            
            # 去重并排序
            domains = sorted(list(set(domains)))
            
        except Exception as e:
            print(f"[-] 解析Censys结果失败: {e}")
        
        return domains

    def scan_domain(self, domain):
        """
        扫描指定域名的证书信息
        
        Args:
            domain (str): 目标域名
            
        Returns:
            list: 使用相同证书的域名列表
        """
        domain = self.extract_domain(domain)
        
        # 步骤1: 查询crt.sh获取证书ID
        first_id, last_id = self.query_crt_sh(domain)
        if not first_id:
            return []
        
        # 步骤2: 获取SHA-256指纹（优先使用第一个ID）
        sha256 = self.get_cert_sha256(first_id)
        if not sha256 and last_id != first_id:
            # 如果第一个失败，尝试最后一个
            sha256 = self.get_cert_sha256(last_id)
        
        if not sha256:
            return []
        
        # 步骤3: 在Censys搜索使用相同证书的域名
        domains = self.search_censys(sha256)
        
        return domains


def main():
    parser = argparse.ArgumentParser(description='证书扫描工具 - 查询使用相同证书的网站')
    parser.add_argument('domain', help='目标域名')
    parser.add_argument('--cookie', help='Censys平台的cookie字符串')
    parser.add_argument('--output', '-o', help='输出结果到文件')
    
    args = parser.parse_args()
    
    # 创建扫描器实例
    scanner = CertScanner(cookie=args.cookie)
    
    print("=" * 60)
    print("证书扫描工具")
    print("=" * 60)
    
    # 执行扫描
    domains = scanner.scan_domain(args.domain)
    
    # 输出结果
    print("\n" + "=" * 60)
    print("扫描结果:")
    print("=" * 60)
    
    if domains:
        print(f"\n找到 {len(domains)} 个使用相同证书的域名:")
        for i, domain in enumerate(domains, 1):
            print(f"{i:3d}. {domain}")
        
        # 保存到文件
        if args.output:
            try:
                with open(args.output, 'w', encoding='utf-8') as f:
                    f.write(f"证书扫描结果 - 目标域名: {args.domain}\n")
                    f.write(f"扫描时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"找到 {len(domains)} 个域名:\n\n")
                    for domain in domains:
                        f.write(f"{domain}\n")
                print(f"\n[+] 结果已保存到: {args.output}")
            except Exception as e:
                print(f"[-] 保存文件失败: {e}")
    else:
        print("\n未找到使用相同证书的域名")
    
    print("\n扫描完成!")


if __name__ == "__main__":
    main()
