#!/usr/bin/env python3
"""
批量证书扫描工具
支持从文件读取多个域名进行批量扫描
"""

import os
import sys
import time
import argparse
import json
from cert_scanner import CertScanner


def read_domains_from_file(file_path):
    """从文件读取域名列表"""
    domains = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                domain = line.strip()
                if domain and not domain.startswith('#'):
                    domains.append(domain)
        return domains
    except Exception as e:
        print(f"[-] 读取域名文件失败: {e}")
        return []


def save_results_json(results, output_file):
    """保存结果为JSON格式"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"[+] 结果已保存到: {output_file}")
    except Exception as e:
        print(f"[-] 保存结果失败: {e}")


def save_results_txt(results, output_file):
    """保存结果为文本格式"""
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("批量证书扫描结果\n")
            f.write("=" * 50 + "\n")
            f.write(f"扫描时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计扫描域名: {len(results)}\n\n")
            
            for i, (domain, data) in enumerate(results.items(), 1):
                f.write(f"{i}. 域名: {domain}\n")
                if data['success']:
                    f.write(f"   状态: 成功\n")
                    f.write(f"   找到域名数量: {len(data['domains'])}\n")
                    f.write(f"   相关域名:\n")
                    for related_domain in data['domains']:
                        f.write(f"     - {related_domain}\n")
                else:
                    f.write(f"   状态: 失败\n")
                    f.write(f"   错误: {data.get('error', '未知错误')}\n")
                f.write("\n")
        
        print(f"[+] 结果已保存到: {output_file}")
    except Exception as e:
        print(f"[-] 保存结果失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='批量证书扫描工具')
    parser.add_argument('input', help='输入文件路径（每行一个域名）')
    parser.add_argument('--cookie', help='Censys平台的cookie字符串')
    parser.add_argument('--output', '-o', help='输出文件路径')
    parser.add_argument('--format', choices=['json', 'txt'], default='txt', help='输出格式')
    parser.add_argument('--delay', type=int, default=2, help='域名间扫描延迟（秒）')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    # 读取域名列表
    domains = read_domains_from_file(args.input)
    if not domains:
        print("[-] 未找到有效的域名")
        sys.exit(1)
    
    print("=" * 60)
    print("批量证书扫描工具")
    print("=" * 60)
    print(f"待扫描域名数量: {len(domains)}")
    print(f"扫描间隔: {args.delay} 秒")
    print("=" * 60)
    
    # 创建扫描器
    scanner = CertScanner(cookie=args.cookie, verbose=args.verbose)
    
    # 批量扫描
    results = {}
    total = len(domains)
    
    for i, domain in enumerate(domains, 1):
        print(f"\n[{i}/{total}] 正在扫描: {domain}")
        print("-" * 40)
        
        try:
            related_domains = scanner.scan_domain(domain)
            results[domain] = {
                'success': True,
                'domains': related_domains,
                'count': len(related_domains)
            }
            print(f"[+] 完成扫描 {domain}，找到 {len(related_domains)} 个相关域名")
            
        except Exception as e:
            results[domain] = {
                'success': False,
                'error': str(e),
                'domains': [],
                'count': 0
            }
            print(f"[-] 扫描 {domain} 失败: {e}")
        
        # 添加延迟（除了最后一个）
        if i < total:
            print(f"等待 {args.delay} 秒...")
            time.sleep(args.delay)
    
    # 输出统计信息
    print("\n" + "=" * 60)
    print("扫描统计:")
    print("=" * 60)
    
    successful = sum(1 for r in results.values() if r['success'])
    failed = total - successful
    total_domains = sum(r['count'] for r in results.values())
    
    print(f"总计扫描: {total} 个域名")
    print(f"成功: {successful} 个")
    print(f"失败: {failed} 个")
    print(f"找到相关域名总数: {total_domains} 个")
    
    # 保存结果
    if args.output:
        if args.format == 'json':
            save_results_json(results, args.output)
        else:
            save_results_txt(results, args.output)
    
    # 显示前10个结果
    print("\n" + "=" * 60)
    print("扫描结果预览 (前10个):")
    print("=" * 60)
    
    count = 0
    for domain, data in results.items():
        if count >= 10:
            break
        
        print(f"\n{count + 1}. {domain}")
        if data['success']:
            print(f"   找到 {data['count']} 个相关域名")
            if data['domains']:
                for related in data['domains'][:5]:  # 只显示前5个
                    print(f"     - {related}")
                if len(data['domains']) > 5:
                    print(f"     ... 还有 {len(data['domains']) - 5} 个")
        else:
            print(f"   扫描失败: {data['error']}")
        
        count += 1
    
    if len(results) > 10:
        print(f"\n... 还有 {len(results) - 10} 个结果")
    
    print("\n批量扫描完成!")


if __name__ == "__main__":
    main()
